#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化文件下载器
支持根据架次号自动登录网站并下载相关文件
"""

import os
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from urllib.parse import urljoin, urlparse
import threading
from datetime import datetime

class AutoDownloader:
    def __init__(self, config_file="config.json"):
        """初始化下载器"""
        self.config_file = config_file
        self.config = self.load_config()
        self.driver = None
        self.is_logged_in = False
        self.download_progress = {}
        
    def load_config(self):
        """加载配置文件"""
        default_config = {
            "website_url": "https://example.com",
            "username": "",
            "password": "",
            "download_folder": "./downloads",
            "chrome_driver_path": "",
            "login_selectors": {
                "username_field": "input[name='username']",
                "password_field": "input[name='password']",
                "login_button": "button[type='submit']"
            },
            "search_selectors": {
                "search_box": "input[name='search']",
                "search_button": "button[type='submit']",
                "result_links": "a[href*='download']"
            },
            "file_categories": {
                "水": ["water", "水", "液体"],
                "燃油": ["fuel", "燃油", "油料"],
                "座椅": ["seat", "座椅", "椅子"]
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        return default_config
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def setup_chrome_driver(self):
        """设置Chrome浏览器"""
        chrome_options = Options()
        
        # 设置下载路径
        download_path = os.path.abspath(self.config["download_folder"])
        if not os.path.exists(download_path):
            os.makedirs(download_path)
        
        prefs = {
            "download.default_directory": download_path,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # 其他选项
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 创建WebDriver
        if self.config["chrome_driver_path"]:
            service = Service(self.config["chrome_driver_path"])
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            self.driver = webdriver.Chrome(options=chrome_options)
        
        # 隐藏自动化特征
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return self.driver
    
    def login(self, username=None, password=None):
        """登录网站"""
        if not self.driver:
            self.setup_chrome_driver()
        
        username = username or self.config["username"]
        password = password or self.config["password"]
        
        if not username or not password:
            raise ValueError("用户名和密码不能为空")
        
        try:
            # 访问登录页面
            self.driver.get(self.config["website_url"])
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            
            # 输入用户名
            username_field = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, self.config["login_selectors"]["username_field"]))
            )
            username_field.clear()
            username_field.send_keys(username)
            
            # 输入密码
            password_field = self.driver.find_element(By.CSS_SELECTOR, self.config["login_selectors"]["password_field"])
            password_field.clear()
            password_field.send_keys(password)
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.CSS_SELECTOR, self.config["login_selectors"]["login_button"])
            login_button.click()
            
            # 等待登录完成
            time.sleep(3)
            
            # 检查是否登录成功（这里需要根据实际网站调整）
            current_url = self.driver.current_url
            if "login" not in current_url.lower() and "error" not in current_url.lower():
                self.is_logged_in = True
                print("✅ 登录成功")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"登录过程中出错: {e}")
            return False
    
    def search_files(self, flight_number, category=None):
        """搜索文件"""
        if not self.is_logged_in:
            raise Exception("请先登录")

        try:
            # 构建搜索关键词
            search_keywords = [flight_number]
            if category and category in self.config["file_categories"]:
                search_keywords.extend(self.config["file_categories"][category])

            search_query = " ".join(search_keywords)

            # 查找搜索框
            search_box = self.driver.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["search_box"])
            search_box.clear()
            search_box.send_keys(search_query)

            # 点击搜索按钮
            search_button = self.driver.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["search_button"])
            search_button.click()

            # 等待搜索结果加载
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, self.config["search_selectors"]["result_container"])))

            # 检查是否有分页，获取所有页面的结果
            all_files = []
            page_num = 1

            while True:
                print(f"正在获取第 {page_num} 页搜索结果...")

                # 获取当前页面的文件信息
                current_page_files = self.get_files_from_current_page(category)
                all_files.extend(current_page_files)

                # 检查是否有下一页
                if not self.go_to_next_page():
                    break

                page_num += 1
                time.sleep(2)  # 等待页面加载

            print(f"搜索完成，共找到 {len(all_files)} 个文件")
            return all_files

        except Exception as e:
            print(f"搜索文件时出错: {e}")
            return []

    def get_files_from_current_page(self, category):
        """获取当前页面的所有文件信息"""
        files_info = []

        try:
            # 获取文件列表容器
            file_containers = self.driver.find_elements(By.CSS_SELECTOR, self.config["search_selectors"]["file_item"])

            for container in file_containers:
                try:
                    # 获取文件标题
                    title_element = container.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["file_title"])
                    title = title_element.text.strip()

                    # 获取下载链接
                    download_element = container.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["download_link"])
                    download_url = download_element.get_attribute("href")

                    # 获取文件大小（如果有）
                    file_size = "未知"
                    try:
                        size_element = container.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["file_size"])
                        file_size = size_element.text.strip()
                    except:
                        pass

                    # 获取文件类型（如果有）
                    file_type = "未知"
                    try:
                        type_element = container.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["file_type"])
                        file_type = type_element.text.strip()
                    except:
                        # 从文件名推断类型
                        if title:
                            if any(ext in title.lower() for ext in ['.pdf', '.doc', '.xls', '.zip']):
                                file_type = title.split('.')[-1].upper()

                    file_info = {
                        "title": title,
                        "url": download_url,
                        "category": category or "未分类",
                        "size": file_size,
                        "type": file_type,
                        "element": download_element  # 保存元素引用用于点击下载
                    }

                    files_info.append(file_info)

                except Exception as e:
                    print(f"获取单个文件信息失败: {e}")
                    continue

        except Exception as e:
            print(f"获取页面文件列表失败: {e}")

        return files_info

    def go_to_next_page(self):
        """跳转到下一页"""
        try:
            # 查找下一页按钮
            next_button = self.driver.find_element(By.CSS_SELECTOR, self.config["search_selectors"]["next_page"])

            # 检查按钮是否可点击
            if "disabled" in next_button.get_attribute("class") or not next_button.is_enabled():
                return False

            # 点击下一页
            next_button.click()
            time.sleep(3)  # 等待页面加载
            return True

        except NoSuchElementException:
            # 没有下一页按钮
            return False
        except Exception as e:
            print(f"翻页失败: {e}")
            return False
    
    def download_file(self, file_info, progress_callback=None):
        """下载单个文件"""
        try:
            title = file_info["title"]
            category = file_info["category"]

            # 创建分类文件夹
            category_folder = os.path.join(self.config["download_folder"], category)
            if not os.path.exists(category_folder):
                os.makedirs(category_folder)

            if progress_callback:
                progress_callback(f"正在下载: {title} ({file_info.get('size', '未知大小')})")

            # 方法1：直接点击下载链接（推荐）
            if "element" in file_info and file_info["element"]:
                try:
                    # 滚动到元素可见
                    self.driver.execute_script("arguments[0].scrollIntoView();", file_info["element"])
                    time.sleep(1)

                    # 点击下载
                    file_info["element"].click()

                    # 等待下载开始
                    time.sleep(3)
                    return True

                except Exception as e:
                    print(f"点击下载失败，尝试URL下载: {e}")

            # 方法2：使用URL下载（备用方案）
            if "url" in file_info and file_info["url"]:
                # 在新标签页中打开下载链接
                self.driver.execute_script(f"window.open('{file_info['url']}', '_blank');")

                # 切换到新标签页
                self.driver.switch_to.window(self.driver.window_handles[-1])
                time.sleep(3)

                # 关闭新标签页，回到原页面
                self.driver.close()
                self.driver.switch_to.window(self.driver.window_handles[0])

                return True

            return False

        except Exception as e:
            print(f"下载文件失败: {e}")
            return False

    def batch_download_files(self, files_list, progress_callback=None):
        """批量下载文件列表"""
        success_count = 0
        total_count = len(files_list)
        failed_files = []

        if progress_callback:
            progress_callback(f"开始批量下载 {total_count} 个文件...")

        for i, file_info in enumerate(files_list):
            try:
                if progress_callback:
                    progress_callback(f"下载进度: {i+1}/{total_count} - {file_info['title']}")

                if self.download_file(file_info, progress_callback):
                    success_count += 1
                    print(f"✅ 下载成功: {file_info['title']}")
                else:
                    failed_files.append(file_info['title'])
                    print(f"❌ 下载失败: {file_info['title']}")

                # 下载间隔，避免请求过快
                time.sleep(self.config.get("download_delay", 2))

            except Exception as e:
                failed_files.append(file_info['title'])
                print(f"❌ 下载出错: {file_info['title']} - {e}")

        # 返回下载结果
        result = {
            "success_count": success_count,
            "total_count": total_count,
            "failed_files": failed_files
        }

        if progress_callback:
            progress_callback(f"批量下载完成！成功: {success_count}/{total_count}")

        return result
    
    def download_by_flight_number(self, flight_number, categories=None, progress_callback=None):
        """根据架次号下载文件"""
        if not self.is_logged_in:
            if not self.login():
                return {"success_count": 0, "total_count": 0, "failed_files": ["登录失败"]}

        categories = categories or ["水", "燃油", "座椅"]
        all_files = []

        # 搜索各类文件
        for category in categories:
            if progress_callback:
                progress_callback(f"正在搜索 {category} 相关文件...")

            files = self.search_files(flight_number, category)
            if files:
                all_files.extend(files)
                if progress_callback:
                    progress_callback(f"找到 {len(files)} 个 {category} 相关文件")
            else:
                if progress_callback:
                    progress_callback(f"未找到 {category} 相关文件")

            time.sleep(2)  # 避免请求过快

        if not all_files:
            if progress_callback:
                progress_callback("未找到任何相关文件")
            return {"success_count": 0, "total_count": 0, "failed_files": ["未找到文件"]}

        # 去重（基于文件标题和URL）
        unique_files = []
        seen_urls = set()
        for file_info in all_files:
            file_key = f"{file_info['title']}_{file_info['url']}"
            if file_key not in seen_urls:
                unique_files.append(file_info)
                seen_urls.add(file_key)

        if progress_callback:
            progress_callback(f"去重后共 {len(unique_files)} 个文件待下载")

        # 批量下载所有文件
        return self.batch_download_files(unique_files, progress_callback)

    def download_all_search_results(self, flight_number, progress_callback=None):
        """下载搜索结果中的所有文件（不分类）"""
        if not self.is_logged_in:
            if not self.login():
                return {"success_count": 0, "total_count": 0, "failed_files": ["登录失败"]}

        if progress_callback:
            progress_callback(f"正在搜索架次号 {flight_number} 的所有文件...")

        # 直接搜索架次号，不加类别限制
        all_files = self.search_files(flight_number)

        if not all_files:
            if progress_callback:
                progress_callback("未找到任何相关文件")
            return {"success_count": 0, "total_count": 0, "failed_files": ["未找到文件"]}

        if progress_callback:
            progress_callback(f"找到 {len(all_files)} 个文件，开始批量下载...")

        # 批量下载所有文件
        return self.batch_download_files(all_files, progress_callback)
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            self.is_logged_in = False

# 使用示例
if __name__ == "__main__":
    downloader = AutoDownloader()
    
    try:
        # 登录
        if downloader.login("your_username", "your_password"):
            # 下载架次号10110的所有文件
            success, total = downloader.download_by_flight_number("10110")
            print(f"下载完成：{success}/{total}")
    finally:
        downloader.close()
