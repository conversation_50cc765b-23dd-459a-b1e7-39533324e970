@echo off
chcp 65001 >nul
cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG 一键测试工具                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 开始自动测试...
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤1: 检查Python环境
echo ═══════════════════════════════════════════════════════════════
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤2: 检查PIL库
echo ═══════════════════════════════════════════════════════════════
python -c "import PIL; print('✅ PIL版本:', PIL.__version__)"
if errorlevel 1 (
    echo ❌ PIL库未安装
    echo 💡 请运行: pip install Pillow
    pause
    exit /b 1
)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤3: 创建测试图片
echo ═══════════════════════════════════════════════════════════════
if not exist "test_images" (
    echo 🔧 创建测试图片...
    python create_test_images.py
) else (
    echo ✅ 测试图片已存在
)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤4: 测试单文件转换
echo ═══════════════════════════════════════════════════════════════
echo 🔧 转换测试文件: test_images/simple/simple_color.tif
python tif.py test_images/simple/simple_color.tif
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤5: 测试批量转换
echo ═══════════════════════════════════════════════════════════════
echo 🔧 批量转换测试文件夹...
python tif.py test_images -o test_converted -r
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 步骤6: 检查转换结果
echo ═══════════════════════════════════════════════════════════════
if exist "test_converted" (
    echo ✅ 批量转换成功，输出目录: test_converted
    dir /s test_converted\*.png
) else (
    echo ❌ 批量转换失败
)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🎉 测试完成！
echo ═══════════════════════════════════════════════════════════════
echo.
echo 📖 现在你可以：
echo    1. 双击 "启动GUI.bat" 使用图形界面
echo    2. 拖拽文件到 "拖拽转换.bat" 进行转换
echo    3. 使用命令行: python tif.py 文件路径
echo.
echo 💡 测试文件位置:
echo    📁 test_images/        - 原始TIF文件
echo    📁 test_converted/     - 转换后的PNG文件
echo.

pause
