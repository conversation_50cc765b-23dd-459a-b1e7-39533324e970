#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF to PNG Converter GUI
TIF转PNG转换工具的图形界面版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
from pathlib import Path
import os
from tif import TifToPngConverter


class TifToPngGUI:
    """TIF转PNG转换器GUI类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("TIF to PNG Converter - TIF转PNG转换工具")
        self.root.geometry("600x500")
        
        # 转换器实例
        self.converter = None
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="TIF to PNG 转换工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 转换模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="转换模式", padding="10")
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        mode_frame.columnconfigure(1, weight=1)
        
        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单文件转换", variable=self.mode_var, 
                       value="single", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="批量转换", variable=self.mode_var, 
                       value="batch", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W)
        
        # 输入文件/目录选择
        input_frame = ttk.LabelFrame(main_frame, text="输入", padding="10")
        input_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)
        
        ttk.Label(input_frame, text="输入路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.input_var = tk.StringVar()
        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_var, width=50)
        self.input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.input_button = ttk.Button(input_frame, text="选择文件", command=self.select_input)
        self.input_button.grid(row=0, column=2)
        
        # 输出目录选择
        output_frame = ttk.LabelFrame(main_frame, text="输出", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.output_button = ttk.Button(output_frame, text="选择目录", command=self.select_output)
        self.output_button.grid(row=0, column=2)
        
        # 选项设置
        options_frame = ttk.LabelFrame(main_frame, text="转换选项", padding="10")
        options_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 质量设置
        ttk.Label(options_frame, text="压缩质量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.quality_var = tk.IntVar(value=95)
        quality_scale = ttk.Scale(options_frame, from_=1, to=100, variable=self.quality_var, 
                                 orient=tk.HORIZONTAL, length=200)
        quality_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.quality_label = ttk.Label(options_frame, text="95")
        self.quality_label.grid(row=0, column=2)
        quality_scale.configure(command=self.update_quality_label)
        
        # 其他选项
        self.optimize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="优化文件大小", 
                       variable=self.optimize_var).grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        
        self.recursive_var = tk.BooleanVar(value=False)
        self.recursive_check = ttk.Checkbutton(options_frame, text="递归处理子目录", 
                                              variable=self.recursive_var)
        self.recursive_check.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        # 转换按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(20, 10))
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                        command=self.start_conversion, style="Accent.TButton")
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="清空", command=self.clear_all)
        self.clear_button.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        ttk.Label(main_frame, textvariable=self.progress_var).grid(row=6, column=0, columnspan=3, pady=(10, 0))
        
        # 日志输出
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 初始化界面状态
        self.on_mode_change()
        
    def on_mode_change(self):
        """模式改变时的处理"""
        mode = self.mode_var.get()
        if mode == "single":
            self.input_button.configure(text="选择文件")
            self.output_button.configure(text="选择文件")
            self.recursive_check.configure(state="disabled")
        else:
            self.input_button.configure(text="选择目录")
            self.output_button.configure(text="选择目录")
            self.recursive_check.configure(state="normal")
    
    def update_quality_label(self, value):
        """更新质量标签"""
        self.quality_label.configure(text=str(int(float(value))))
    
    def select_input(self):
        """选择输入文件或目录"""
        mode = self.mode_var.get()
        if mode == "single":
            filename = filedialog.askopenfilename(
                title="选择TIF文件",
                filetypes=[("TIF files", "*.tif *.tiff"), ("All files", "*.*")]
            )
            if filename:
                self.input_var.set(filename)
        else:
            dirname = filedialog.askdirectory(title="选择输入目录")
            if dirname:
                self.input_var.set(dirname)
    
    def select_output(self):
        """选择输出文件或目录"""
        mode = self.mode_var.get()
        if mode == "single":
            filename = filedialog.asksaveasfilename(
                title="保存PNG文件",
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
            )
            if filename:
                self.output_var.set(filename)
        else:
            dirname = filedialog.askdirectory(title="选择输出目录")
            if dirname:
                self.output_var.set(dirname)
    
    def clear_all(self):
        """清空所有输入"""
        self.input_var.set("")
        self.output_var.set("")
        self.log_text.delete(1.0, tk.END)
        self.progress_var.set("准备就绪")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_conversion(self):
        """开始转换"""
        input_path = self.input_var.get().strip()
        output_path = self.output_var.get().strip()
        
        if not input_path:
            messagebox.showerror("错误", "请选择输入文件或目录")
            return
        
        if not Path(input_path).exists():
            messagebox.showerror("错误", "输入路径不存在")
            return
        
        # 在新线程中执行转换
        self.convert_button.configure(state="disabled")
        self.progress_var.set("转换中...")
        
        thread = threading.Thread(target=self.do_conversion, args=(input_path, output_path))
        thread.daemon = True
        thread.start()
    
    def do_conversion(self, input_path, output_path):
        """执行转换（在后台线程中）"""
        try:
            # 创建转换器
            converter = TifToPngConverter(
                quality=self.quality_var.get(),
                optimize=self.optimize_var.get()
            )
            
            mode = self.mode_var.get()
            
            if mode == "single":
                # 单文件转换
                self.log_message(f"开始转换文件: {input_path}")
                success = converter.convert_single_file(input_path, output_path or None)
                if success:
                    self.log_message("转换完成!")
                    self.progress_var.set("转换成功")
                else:
                    self.log_message("转换失败!")
                    self.progress_var.set("转换失败")
            else:
                # 批量转换
                self.log_message(f"开始批量转换目录: {input_path}")
                converter.convert_batch(
                    input_path, 
                    output_path or None, 
                    self.recursive_var.get()
                )
                self.log_message(f"批量转换完成! 成功: {converter.converted_count}, 失败: {converter.failed_count}")
                self.progress_var.set(f"转换完成 - 成功: {converter.converted_count}, 失败: {converter.failed_count}")
                
        except Exception as e:
            self.log_message(f"转换过程中发生错误: {str(e)}")
            self.progress_var.set("转换出错")
        finally:
            # 重新启用转换按钮
            self.root.after(0, lambda: self.convert_button.configure(state="normal"))


def main():
    """主函数"""
    root = tk.Tk()
    app = TifToPngGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
