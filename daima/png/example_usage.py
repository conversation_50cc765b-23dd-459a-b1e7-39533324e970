#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF to PNG Converter 使用示例
演示如何使用TIF转PNG转换工具
"""

from tif import TifToPngConverter
from pathlib import Path


def example_single_file():
    """单文件转换示例"""
    print("=== 单文件转换示例 ===")
    
    # 创建转换器
    converter = TifToPngConverter(quality=95, optimize=True)
    
    # 假设有一个test.tif文件
    input_file = "test.tif"
    output_file = "test_converted.png"
    
    # 转换文件
    success = converter.convert_single_file(input_file, output_file)
    
    if success:
        print(f"转换成功: {input_file} -> {output_file}")
    else:
        print(f"转换失败: {input_file}")


def example_batch_conversion():
    """批量转换示例"""
    print("\n=== 批量转换示例 ===")
    
    # 创建转换器
    converter = TifToPngConverter(quality=90, optimize=True)
    
    # 批量转换当前目录下的所有TIF文件
    input_dir = "."
    output_dir = "./converted_png"
    
    # 转换文件（不递归）
    converter.convert_batch(input_dir, output_dir, recursive=False)
    
    print(f"批量转换完成，输出目录: {output_dir}")


def example_recursive_conversion():
    """递归批量转换示例"""
    print("\n=== 递归批量转换示例 ===")
    
    # 创建转换器
    converter = TifToPngConverter(quality=85, optimize=True)
    
    # 递归转换目录及其子目录下的所有TIF文件
    input_dir = "./images"
    output_dir = "./converted_images"
    
    # 转换文件（递归）
    converter.convert_batch(input_dir, output_dir, recursive=True)
    
    print(f"递归转换完成，输出目录: {output_dir}")


def create_test_structure():
    """创建测试目录结构"""
    print("\n=== 创建测试目录结构 ===")
    
    # 创建测试目录
    test_dirs = [
        "test_images",
        "test_images/subfolder1",
        "test_images/subfolder2"
    ]
    
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")
    
    print("测试目录结构创建完成")
    print("请将一些TIF文件放入这些目录中进行测试")


if __name__ == "__main__":
    print("TIF to PNG Converter 使用示例")
    print("=" * 50)
    
    # 创建测试目录结构
    create_test_structure()
    
    # 运行示例（注释掉实际转换，因为可能没有测试文件）
    print("\n以下是使用示例代码（需要有实际的TIF文件才能运行）:")
    print("1. 单文件转换")
    print("2. 批量转换")
    print("3. 递归批量转换")
    
    # 如果有测试文件，可以取消注释以下行
    # example_single_file()
    # example_batch_conversion()
    # example_recursive_conversion()
