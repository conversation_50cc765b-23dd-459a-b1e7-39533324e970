🎯 TIF转PNG工具 - 简单使用指南

═══════════════════════════════════════════════════════════════

📋 方法1：图形界面（推荐新手使用）
═══════════════════════════════════════════════════════════════

1️⃣ 启动方式：
   方式A：双击文件 "启动GUI.bat"
   方式B：在命令行输入：python tif_to_png_gui.py

2️⃣ 使用步骤：
   ✅ 选择转换模式（单文件 或 批量转换）
   ✅ 点击"选择文件"或"选择目录"按钮
   ✅ 选择输出位置（可选，默认同目录）
   ✅ 调整质量设置（默认95已经很好）
   ✅ 点击"开始转换"按钮
   ✅ 查看转换日志

═══════════════════════════════════════════════════════════════

📋 方法2：命令行使用（适合批量处理）
═══════════════════════════════════════════════════════════════

🔧 打开命令提示符（cmd）或PowerShell，然后：

1️⃣ 转换单个文件：
   python daima/png/tif.py 文件路径.tif

   例子：
   python daima/png/tif.py test_images/simple/simple_color.tif

2️⃣ 批量转换整个文件夹：
   python daima/png/tif.py 文件夹路径 -o 输出文件夹

   例子：
   python daima/png/tif.py test_images -o converted_images

3️⃣ 递归转换（包括子文件夹）：
   python daima/png/tif.py 文件夹路径 -o 输出文件夹 -r

   例子：
   python daima/png/tif.py test_images -o converted_images -r

═══════════════════════════════════════════════════════════════

📋 方法3：拖拽使用（最简单）
═══════════════════════════════════════════════════════════════

我来为你创建一个拖拽脚本...

═══════════════════════════════════════════════════════════════

🎯 测试文件位置
═══════════════════════════════════════════════════════════════

我已经为你创建了测试文件，位置在：
📁 test_images/simple/     - 简单图片
📁 test_images/complex/    - 复杂图片  
📁 test_images/transparent/ - 透明图片

你可以用这些文件来测试转换功能！

═══════════════════════════════════════════════════════════════

❓ 常见问题
═══════════════════════════════════════════════════════════════

Q: 双击bat文件没反应？
A: 右键点击bat文件，选择"以管理员身份运行"

Q: 提示找不到python？
A: 确保已安装Python，或在命令行中用完整路径

Q: 转换失败？
A: 检查文件是否为TIF格式，是否有读取权限

Q: 想要更高质量？
A: 在GUI中调整质量滑块到95-100，或命令行加 -q 100

═══════════════════════════════════════════════════════════════

🚀 快速开始
═══════════════════════════════════════════════════════════════

最简单的方法：
1. 双击 "启动GUI.bat"
2. 选择一个测试文件（比如 test_images/simple/simple_color.tif）
3. 点击"开始转换"
4. 查看生成的PNG文件

就这么简单！
