#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF转PNG转换工具打包脚本
使用cx_Freeze将Python程序打包成exe文件
"""

from cx_Freeze import setup, Executable
import sys
import os

# 构建选项
build_exe_options = {
    'packages': [
        'tkinter', 
        'PIL', 
        'os', 
        'sys', 
        'threading', 
        'pathlib'
    ],
    'excludes': [
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'test',
        'unittest',
        'distutils'
    ],
    'include_files': [
        ('tif.py', 'tif.py'),  # 包含转换核心模块
        ('使用方法.txt', '使用方法.txt'),  # 包含使用说明
    ],
    'optimize': 2,  # 优化级别
    'zip_include_packages': ['*'],  # 压缩包
    'zip_exclude_packages': [],
}

# 如果存在测试图片，也包含进去
if os.path.exists('test_images'):
    build_exe_options['include_files'].append(('test_images', 'test_images'))

# 如果存在图标文件，使用它
icon_file = None
for icon_name in ['icon.ico', 'app.ico', 'tif.ico']:
    if os.path.exists(icon_name):
        icon_file = icon_name
        break

# 创建可执行文件配置
executables = [
    Executable(
        'tif_to_png_gui.py',
        base='Win32GUI',  # 使用GUI基础，避免显示控制台窗口
        target_name='TIF转PNG转换工具.exe',
        icon=icon_file,
        shortcut_name='TIF转PNG转换工具',
        shortcut_dir='DesktopFolder'  # 在桌面创建快捷方式
    )
]

# 设置信息
setup(
    name='TIF转PNG转换工具',
    version='1.0.0',
    description='简单易用的TIF格式转PNG格式转换工具',
    author='TIF转换工具开发团队',
    options={'build_exe': build_exe_options},
    executables=executables
)
