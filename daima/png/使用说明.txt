🎯 TIF转PNG工具 - 超简单使用指南

═══════════════════════════════════════════════════════════════

🚀 最简单的使用方法（推荐）
═══════════════════════════════════════════════════════════════

1. 打开命令提示符（按Win+R，输入cmd，回车）

2. 进入工具目录：
   cd /d D:\shangfei\sf\11\daima\png

3. 转换单个文件：
   python tif.py test_images\simple\simple_color.tif

4. 批量转换文件夹：
   python tif.py test_images -o converted -r

就这么简单！

═══════════════════════════════════════════════════════════════

📱 图形界面使用（更直观）
═══════════════════════════════════════════════════════════════

1. 双击文件：启动GUI.bat
   （如果不行，在命令行输入：python tif_to_png_gui.py）

2. 在弹出的窗口中：
   ✅ 选择"单文件转换"或"批量转换"
   ✅ 点击"选择文件"按钮选择TIF文件
   ✅ 点击"开始转换"
   ✅ 等待转换完成

═══════════════════════════════════════════════════════════════

🎯 测试文件位置
═══════════════════════════════════════════════════════════════

我已经为你准备了测试文件：
📁 test_images\simple\simple_color.tif    - 彩色图片
📁 test_images\simple\grayscale.tif       - 灰度图片
📁 test_images\transparent\transparent.tif - 透明图片
📁 test_images\complex\complex_image.tif   - 复杂图片

你可以用这些文件来测试！

═══════════════════════════════════════════════════════════════

💡 常用命令示例
═══════════════════════════════════════════════════════════════

# 转换单个文件到指定位置
python tif.py input.tif -o output.png

# 批量转换，保持目录结构
python tif.py input_folder -o output_folder -r

# 设置高质量
python tif.py input.tif -q 100

# 查看帮助
python tif.py --help

═══════════════════════════════════════════════════════════════

❓ 遇到问题？
═══════════════════════════════════════════════════════════════

问题1：提示"python不是内部命令"
解决：确保已安装Python并添加到系统PATH

问题2：提示"No module named PIL"
解决：运行 pip install Pillow

问题3：转换失败
解决：检查文件是否为TIF格式，路径是否正确

问题4：权限错误
解决：以管理员身份运行命令提示符

═══════════════════════════════════════════════════════════════

🎉 快速开始
═══════════════════════════════════════════════════════════════

最快的测试方法：
1. 打开命令提示符
2. 复制粘贴这个命令：
   cd /d D:\shangfei\sf\11\daima\png && python tif.py test_images\simple\simple_color.tif
3. 按回车，看到转换成功！

就是这么简单！
