@echo off
cd /d "%~dp0"

echo.
echo ========================================
echo TIF to PNG Converter - Quick Test
echo ========================================
echo.

echo Step 1: Check Python...
python --version
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Step 2: Test single file conversion...
python tif.py test_images/simple/simple_color.tif
echo.

echo Step 3: Test batch conversion...
python tif.py test_images -o test_output -r
echo.

echo ========================================
echo Test completed!
echo ========================================
echo.
echo You can now:
echo 1. Double-click "启动GUI.bat" for GUI
echo 2. Use command: python tif.py [file_path]
echo.
pause
