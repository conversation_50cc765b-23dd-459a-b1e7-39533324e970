#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用的TIF图片
"""

from PIL import Image, ImageDraw, ImageFont
import os
import numpy as np

def create_test_images():
    """创建多种类型的TIF测试图片"""
    
    # 创建测试图片目录
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    print("🎨 开始创建测试图片...")
    
    # 1. 创建RGB彩色图片
    print("📸 创建RGB彩色图片...")
    img_rgb = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img_rgb)
    
    # 绘制彩色矩形
    draw.rectangle([50, 50, 150, 150], fill='red', outline='black', width=2)
    draw.rectangle([200, 50, 300, 150], fill='green', outline='black', width=2)
    draw.rectangle([125, 175, 225, 275], fill='blue', outline='black', width=2)
    
    # 添加文字
    try:
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 20), "RGB Color Test", fill='black', font=font)
    img_rgb.save(os.path.join(test_dir, "test_rgb.tif"), format='TIFF')
    
    # 2. 创建RGBA透明图片
    print("🌈 创建RGBA透明图片...")
    img_rgba = Image.new('RGBA', (400, 300), color=(255, 255, 255, 0))
    draw = ImageDraw.Draw(img_rgba)
    
    # 绘制半透明圆形
    draw.ellipse([50, 50, 150, 150], fill=(255, 0, 0, 128), outline=(0, 0, 0, 255), width=3)
    draw.ellipse([200, 50, 300, 150], fill=(0, 255, 0, 128), outline=(0, 0, 0, 255), width=3)
    draw.ellipse([125, 175, 225, 275], fill=(0, 0, 255, 128), outline=(0, 0, 0, 255), width=3)
    
    draw.text((50, 20), "RGBA Transparent", fill=(0, 0, 0, 255), font=font)
    img_rgba.save(os.path.join(test_dir, "test_rgba.tif"), format='TIFF')
    
    # 3. 创建灰度图片
    print("⚫ 创建灰度图片...")
    img_gray = Image.new('L', (400, 300), color=255)
    draw = ImageDraw.Draw(img_gray)
    
    # 绘制不同灰度的图形
    for i in range(5):
        gray_value = 50 + i * 40
        x = 50 + i * 60
        draw.rectangle([x, 100, x + 50, 200], fill=gray_value, outline=0, width=2)
    
    draw.text((50, 50), "Grayscale Test", fill=0, font=font)
    img_gray.save(os.path.join(test_dir, "test_grayscale.tif"), format='TIFF')
    
    # 4. 创建调色板模式图片
    print("🎨 创建调色板图片...")
    img_p = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img_p)
    
    # 使用有限的颜色
    colors = ['red', 'green', 'blue', 'yellow', 'cyan', 'magenta']
    for i, color in enumerate(colors):
        x = (i % 3) * 120 + 50
        y = (i // 3) * 100 + 80
        draw.rectangle([x, y, x + 80, y + 60], fill=color, outline='black', width=2)
    
    draw.text((50, 20), "Palette Mode Test", fill='black', font=font)
    # 转换为调色板模式
    img_p = img_p.convert('P', palette=Image.ADAPTIVE, colors=256)
    img_p.save(os.path.join(test_dir, "test_palette.tif"), format='TIFF')
    
    # 5. 创建大尺寸图片（测试性能）
    print("📏 创建大尺寸图片...")
    img_large = Image.new('RGB', (1920, 1080), color='lightblue')
    draw = ImageDraw.Draw(img_large)
    
    # 绘制网格
    for x in range(0, 1920, 100):
        draw.line([(x, 0), (x, 1080)], fill='white', width=1)
    for y in range(0, 1080, 100):
        draw.line([(0, y), (1920, y)], fill='white', width=1)
    
    # 添加标题
    try:
        large_font = ImageFont.truetype("arial.ttf", 48)
    except:
        large_font = ImageFont.load_default()
    
    draw.text((50, 50), "Large Size Test (1920x1080)", fill='darkblue', font=large_font)
    
    # 绘制一些图形
    draw.ellipse([400, 200, 800, 600], fill='yellow', outline='orange', width=5)
    draw.rectangle([1000, 300, 1500, 700], fill='lightgreen', outline='darkgreen', width=5)
    
    img_large.save(os.path.join(test_dir, "test_large.tif"), format='TIFF')
    
    # 6. 创建渐变图片
    print("🌅 创建渐变图片...")
    img_gradient = Image.new('RGB', (400, 300), color='white')
    pixels = img_gradient.load()
    
    for x in range(400):
        for y in range(300):
            # 创建彩虹渐变
            r = int(255 * (x / 400))
            g = int(255 * (y / 300))
            b = int(255 * ((x + y) / 700))
            pixels[x, y] = (r, g, b)
    
    draw = ImageDraw.Draw(img_gradient)
    draw.text((50, 20), "Gradient Test", fill='white', font=font)
    img_gradient.save(os.path.join(test_dir, "test_gradient.tif"), format='TIFF')
    
    # 7. 创建黑白二值图片
    print("⚪ 创建黑白二值图片...")
    img_bw = Image.new('1', (400, 300), color=1)  # 1表示白色
    draw = ImageDraw.Draw(img_bw)
    
    # 绘制黑白图案
    draw.rectangle([50, 50, 350, 250], fill=0, outline=0)  # 黑色矩形
    draw.ellipse([100, 100, 300, 200], fill=1, outline=1)  # 白色椭圆
    draw.text((120, 130), "B&W Test", fill=0, font=font)
    
    img_bw.save(os.path.join(test_dir, "test_blackwhite.tif"), format='TIFF')
    
    print("✅ 测试图片创建完成！")
    print(f"📁 图片保存在目录: {os.path.abspath(test_dir)}")
    
    # 显示创建的文件列表
    files = os.listdir(test_dir)
    tif_files = [f for f in files if f.endswith('.tif')]
    
    print(f"\n📋 创建的测试图片 ({len(tif_files)} 个):")
    for i, filename in enumerate(tif_files, 1):
        filepath = os.path.join(test_dir, filename)
        file_size = os.path.getsize(filepath)
        size_kb = file_size / 1024
        
        # 获取图片信息
        with Image.open(filepath) as img:
            mode = img.mode
            size = img.size
        
        print(f"  {i}. {filename}")
        print(f"     尺寸: {size[0]}x{size[1]}, 模式: {mode}, 大小: {size_kb:.1f}KB")
    
    print(f"\n🎯 现在你可以使用这些图片测试TIF转PNG工具了！")
    print(f"💡 建议先用小图片测试，再用大图片测试性能。")

if __name__ == "__main__":
    create_test_images()
