#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用的TIF图片
生成各种类型的TIF图片用于测试转换功能
"""

from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path
import random


def create_test_directory():
    """创建测试目录结构"""
    test_dirs = [
        "test_images",
        "test_images/simple",
        "test_images/complex",
        "test_images/transparent"
    ]
    
    for dir_path in test_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    return test_dirs


def create_simple_tif():
    """创建简单的彩色TIF图片"""
    # 创建一个简单的彩色图片
    img = Image.new('RGB', (400, 300), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制一些简单的图形
    draw.rectangle([50, 50, 150, 150], fill='red', outline='black', width=3)
    draw.ellipse([200, 50, 350, 200], fill='green', outline='blue', width=3)
    draw.polygon([(100, 200), (150, 250), (200, 200), (175, 280), (125, 280)], 
                fill='yellow', outline='purple')
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    draw.text((50, 20), "测试图片 - 简单彩色", fill='black', font=font)
    
    # 保存为TIF
    img.save("test_images/simple/simple_color.tif", "TIFF")
    print("✓ 创建简单彩色TIF: test_images/simple/simple_color.tif")


def create_grayscale_tif():
    """创建灰度TIF图片"""
    # 创建灰度图片
    img = Image.new('L', (300, 200), color=128)
    draw = ImageDraw.Draw(img)
    
    # 绘制渐变效果
    for i in range(300):
        color = int(255 * i / 300)
        draw.line([(i, 0), (i, 100)], fill=color)
    
    # 绘制一些图形
    draw.rectangle([50, 120, 150, 180], fill=200, outline=0, width=2)
    draw.ellipse([180, 120, 250, 180], fill=100, outline=255, width=2)
    
    img.save("test_images/simple/grayscale.tif", "TIFF")
    print("✓ 创建灰度TIF: test_images/simple/grayscale.tif")


def create_transparent_tif():
    """创建带透明度的TIF图片"""
    # 创建RGBA图片
    img = Image.new('RGBA', (300, 300), color=(255, 255, 255, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制半透明的图形
    draw.ellipse([50, 50, 150, 150], fill=(255, 0, 0, 128))  # 半透明红色
    draw.ellipse([100, 100, 200, 200], fill=(0, 255, 0, 128))  # 半透明绿色
    draw.ellipse([125, 75, 225, 175], fill=(0, 0, 255, 128))  # 半透明蓝色
    
    # 添加完全不透明的边框
    draw.rectangle([10, 10, 290, 290], fill=None, outline=(0, 0, 0, 255), width=3)
    
    img.save("test_images/transparent/transparent.tif", "TIFF")
    print("✓ 创建透明TIF: test_images/transparent/transparent.tif")


def create_complex_tif():
    """创建复杂的TIF图片"""
    # 创建大尺寸的复杂图片
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制网格背景
    for i in range(0, 800, 20):
        draw.line([(i, 0), (i, 600)], fill='lightgray')
    for i in range(0, 600, 20):
        draw.line([(0, i), (800, i)], fill='lightgray')
    
    # 绘制随机彩色圆圈
    for _ in range(50):
        x = random.randint(0, 750)
        y = random.randint(0, 550)
        r = random.randint(10, 50)
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        draw.ellipse([x, y, x+r, y+r], fill=color, outline='black')
    
    # 绘制一些文字
    try:
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        font = ImageFont.load_default()
    
    draw.text((50, 50), "复杂测试图片", fill='black', font=font)
    draw.text((50, 80), "包含多种颜色和图形", fill='blue', font=font)
    draw.text((50, 110), "用于测试转换性能", fill='red', font=font)
    
    img.save("test_images/complex/complex_image.tif", "TIFF")
    print("✓ 创建复杂TIF: test_images/complex/complex_image.tif")


def create_palette_tif():
    """创建调色板模式的TIF图片"""
    # 创建调色板图片
    img = Image.new('P', (200, 200))
    
    # 设置调色板（256色）
    palette = []
    for i in range(256):
        palette.extend([i, (255-i)//2, (i*2) % 256])  # RGB
    img.putpalette(palette)
    
    # 绘制一些图案
    pixels = []
    for y in range(200):
        row = []
        for x in range(200):
            # 创建一个简单的图案
            if (x // 20 + y // 20) % 2 == 0:
                row.append(x % 256)
            else:
                row.append(y % 256)
        pixels.extend(row)
    
    img.putdata(pixels)
    img.save("test_images/simple/palette.tif", "TIFF")
    print("✓ 创建调色板TIF: test_images/simple/palette.tif")


def create_black_white_tif():
    """创建黑白二值TIF图片"""
    # 创建黑白图片
    img = Image.new('1', (300, 200), color=1)  # 白色背景
    draw = ImageDraw.Draw(img)
    
    # 绘制黑色图形
    draw.rectangle([50, 50, 150, 150], fill=0)  # 黑色矩形
    draw.ellipse([180, 50, 250, 120], fill=0)   # 黑色圆形
    
    # 绘制一些线条
    for i in range(10):
        y = 160 + i * 4
        draw.line([(20, y), (280, y)], fill=0)
    
    img.save("test_images/simple/black_white.tif", "TIFF")
    print("✓ 创建黑白TIF: test_images/simple/black_white.tif")


def create_large_tif():
    """创建大尺寸TIF图片（用于性能测试）"""
    # 创建大图片
    img = Image.new('RGB', (2000, 1500), color='lightcyan')
    draw = ImageDraw.Draw(img)
    
    # 绘制大量小图形
    for i in range(100):
        x = random.randint(0, 1900)
        y = random.randint(0, 1400)
        w = random.randint(20, 100)
        h = random.randint(20, 100)
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        
        if i % 2 == 0:
            draw.rectangle([x, y, x+w, y+h], fill=color, outline='black')
        else:
            draw.ellipse([x, y, x+w, y+h], fill=color, outline='black')
    
    # 添加标题
    try:
        font = ImageFont.truetype("arial.ttf", 48)
    except:
        font = ImageFont.load_default()
    
    draw.text((100, 100), "大尺寸测试图片 (2000x1500)", fill='black', font=font)
    
    img.save("test_images/complex/large_image.tif", "TIFF")
    print("✓ 创建大尺寸TIF: test_images/complex/large_image.tif")


def main():
    """主函数"""
    print("开始创建测试TIF图片...")
    print("=" * 50)
    
    # 创建目录
    create_test_directory()
    print("✓ 创建测试目录结构")
    
    # 创建各种类型的测试图片
    try:
        create_simple_tif()
        create_grayscale_tif()
        create_transparent_tif()
        create_complex_tif()
        create_palette_tif()
        create_black_white_tif()
        create_large_tif()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试图片创建完成！")
        print("\n测试图片位置:")
        print("📁 test_images/simple/     - 简单图片")
        print("📁 test_images/complex/    - 复杂图片")
        print("📁 test_images/transparent/ - 透明图片")
        
        print("\n现在你可以使用以下命令测试转换:")
        print("🔧 单文件测试:")
        print("   python tif.py test_images/simple/simple_color.tif")
        print("\n🔧 批量测试:")
        print("   python tif.py test_images -o converted_images -r")
        print("\n🔧 GUI测试:")
        print("   python tif_to_png_gui.py")
        
    except Exception as e:
        print(f"❌ 创建测试图片时出错: {e}")


if __name__ == "__main__":
    main()
