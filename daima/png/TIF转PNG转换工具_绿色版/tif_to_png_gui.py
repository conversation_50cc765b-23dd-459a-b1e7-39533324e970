#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF to PNG Converter GUI
TIF转PNG转换工具的图形界面版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import sys
from pathlib import Path
from PIL import Image

# 导入转换功能
from tif import tif_to_png, batch_convert


class TifToPngGUI:
    """TIF转PNG转换器GUI类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("TIF to PNG Converter - TIF转PNG转换工具")
        self.root.geometry("700x600")
        self.root.configure(bg='#f0f0f0')
        
        # 转换状态
        self.is_converting = False
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="TIF to PNG 转换工具", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))
        
        # 转换模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="转换模式", padding="10")
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        
        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单文件转换", variable=self.mode_var, 
                       value="single", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="批量转换", variable=self.mode_var, 
                       value="batch", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W)
        
        # 输入文件/目录选择
        input_frame = ttk.LabelFrame(main_frame, text="输入", padding="10")
        input_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        input_frame.columnconfigure(1, weight=1)
        
        ttk.Label(input_frame, text="输入路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.input_var = tk.StringVar()
        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_var, width=50)
        self.input_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.input_button = ttk.Button(input_frame, text="选择文件", command=self.select_input)
        self.input_button.grid(row=0, column=2)
        
        # 输出目录选择
        output_frame = ttk.LabelFrame(main_frame, text="输出", padding="10")
        output_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="输出路径:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        self.output_button = ttk.Button(output_frame, text="选择目录", command=self.select_output)
        self.output_button.grid(row=0, column=2)
        
        # 提示信息
        tip_label = ttk.Label(output_frame, text="💡 提示：输出路径为空时，将在原文件目录生成PNG文件", 
                             foreground="gray")
        tip_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 转换按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(20, 15))
        
        self.convert_button = ttk.Button(button_frame, text="🚀 开始转换", 
                                        command=self.start_conversion)
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_button = ttk.Button(button_frame, text="🗑️ 清空", 
                                      command=self.clear_all)
        self.clear_button.pack(side=tk.LEFT)
        
        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="准备就绪")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=12, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 初始化界面状态
        self.on_mode_change()
        self.log_message("🎉 TIF转PNG转换工具已启动，请选择文件开始转换")
        
    def on_mode_change(self):
        """模式改变时的处理"""
        mode = self.mode_var.get()
        if mode == "single":
            self.input_button.configure(text="选择文件")
            self.output_button.configure(text="选择文件")
        else:
            self.input_button.configure(text="选择目录")
            self.output_button.configure(text="选择目录")
        
        # 清空路径
        self.input_var.set("")
        self.output_var.set("")
    
    def select_input(self):
        """选择输入文件或目录"""
        mode = self.mode_var.get()
        if mode == "single":
            file_path = filedialog.askopenfilename(
                title="选择TIF文件",
                filetypes=[("TIF文件", "*.tif *.tiff"), ("所有文件", "*.*")]
            )
            if file_path:
                self.input_var.set(file_path)
        else:
            dir_path = filedialog.askdirectory(title="选择包含TIF文件的目录")
            if dir_path:
                self.input_var.set(dir_path)
    
    def select_output(self):
        """选择输出文件或目录"""
        mode = self.mode_var.get()
        if mode == "single":
            file_path = filedialog.asksaveasfilename(
                title="保存PNG文件",
                defaultextension=".png",
                filetypes=[("PNG文件", "*.png"), ("所有文件", "*.*")]
            )
            if file_path:
                self.output_var.set(file_path)
        else:
            dir_path = filedialog.askdirectory(title="选择输出目录")
            if dir_path:
                self.output_var.set(dir_path)
    
    def clear_all(self):
        """清空所有输入"""
        self.input_var.set("")
        self.output_var.set("")
        self.log_text.delete(1.0, tk.END)
        self.progress_var.set("准备就绪")
        self.log_message("🗑️ 已清空所有输入")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_conversion(self):
        """开始转换"""
        if self.is_converting:
            return
        
        input_path = self.input_var.get().strip()
        output_path = self.output_var.get().strip()
        
        if not input_path:
            messagebox.showerror("错误", "请选择输入文件或目录！")
            return
        
        if not os.path.exists(input_path):
            messagebox.showerror("错误", "输入路径不存在！")
            return
        
        # 在新线程中执行转换
        thread = threading.Thread(target=self.convert_files, args=(input_path, output_path))
        thread.daemon = True
        thread.start()
    
    def convert_files(self, input_path, output_path):
        """执行文件转换"""
        try:
            self.is_converting = True
            self.convert_button.configure(state="disabled")
            self.progress_bar.start()
            
            mode = self.mode_var.get()
            
            if mode == "single":
                # 单文件转换
                self.progress_var.set("正在转换文件...")
                self.log_message(f"🔄 开始转换: {os.path.basename(input_path)}")
                
                output_file = output_path if output_path else None
                success = tif_to_png(input_path, output_file)
                
                if success:
                    self.log_message(f"✅ 转换成功!")
                    self.progress_var.set("转换完成")
                    messagebox.showinfo("完成", "文件转换成功！")
                else:
                    self.log_message(f"❌ 转换失败")
                    self.progress_var.set("转换失败")
                    messagebox.showerror("错误", "文件转换失败！")
            
            else:
                # 批量转换
                self.progress_var.set("正在批量转换...")
                self.log_message(f"🔄 开始批量转换目录: {input_path}")
                
                # 使用修改后的批量转换函数
                self.batch_convert_with_progress(input_path, output_path)
        
        except Exception as e:
            self.log_message(f"❌ 转换过程中发生错误: {str(e)}")
            messagebox.showerror("错误", f"转换失败：{str(e)}")
        
        finally:
            self.is_converting = False
            self.convert_button.configure(state="normal")
            self.progress_bar.stop()
    
    def batch_convert_with_progress(self, input_dir, output_dir):
        """带进度显示的批量转换"""
        if not os.path.isdir(input_dir):
            self.log_message(f"❌ 错误: 目录不存在 - {input_dir}")
            return
        
        # 查找TIF文件
        tif_files = []
        for file in os.listdir(input_dir):
            if file.lower().endswith(('.tif', '.tiff')):
                tif_files.append(os.path.join(input_dir, file))
        
        if not tif_files:
            self.log_message(f"⚠️ 在目录 {input_dir} 中未找到TIF文件")
            messagebox.showwarning("警告", "未找到TIF文件！")
            return
        
        # 创建输出目录
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            self.log_message(f"📁 创建输出目录: {output_dir}")
        
        self.log_message(f"📊 找到 {len(tif_files)} 个TIF文件，开始转换...")
        
        success_count = 0
        for i, tif_file in enumerate(tif_files, 1):
            self.progress_var.set(f"转换中... ({i}/{len(tif_files)})")
            self.log_message(f"[{i}/{len(tif_files)}] 🔄 处理: {os.path.basename(tif_file)}")
            
            if output_dir:
                output_file = os.path.join(output_dir, os.path.basename(tif_file).rsplit('.', 1)[0] + '.png')
            else:
                output_file = None
            
            if tif_to_png(tif_file, output_file):
                success_count += 1
                self.log_message(f"    ✅ 转换成功")
            else:
                self.log_message(f"    ❌ 转换失败")
        
        self.log_message(f"🎉 批量转换完成! 成功: {success_count}/{len(tif_files)}")
        self.progress_var.set(f"转换完成: {success_count}/{len(tif_files)}")
        
        if success_count > 0:
            messagebox.showinfo("完成", f"批量转换完成！\n成功转换 {success_count}/{len(tif_files)} 个文件")
        else:
            messagebox.showwarning("警告", "没有文件转换成功！")


def main():
    """主函数"""
    root = tk.Tk()
    app = TifToPngGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()


if __name__ == "__main__":
    main()
