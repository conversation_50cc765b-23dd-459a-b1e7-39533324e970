@echo off
chcp 65001 >nul
title TIF转PNG转换工具 - 绿色版

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG转换工具                          ║
echo ║                      绿色免安装版                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

echo 🔍 正在检查运行环境...
echo.

REM 检查是否有便携版Python
if exist "python_portable\python.exe" (
    echo ✅ 发现便携版Python环境
    set PYTHON_CMD=python_portable\python.exe
    goto :check_libs
)

REM 检查系统Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python环境
    echo.
    echo 💡 解决方案（选择其一）：
    echo.
    echo 【方案1：安装系统Python（推荐）】
    echo 1. 访问 https://www.python.org/downloads/
    echo 2. 下载Python 3.7或更高版本
    echo 3. 安装时勾选 "Add Python to PATH"
    echo 4. 重启电脑后再运行此程序
    echo.
    echo 【方案2：使用便携版Python（高级用户）】
    echo 1. 下载便携版Python
    echo 2. 解压到当前目录，重命名为 "python_portable"
    echo 3. 重新运行此程序
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
) else (
    echo ✅ 系统Python环境可用
    python --version
    set PYTHON_CMD=python
)

:check_libs
echo.
echo 🔍 检查必要组件...

REM 检查PIL库
%PYTHON_CMD% -c "import PIL; print('✅ 图像处理库可用')" 2>nul
if errorlevel 1 (
    echo ❌ 图像处理库未安装
    echo 💡 正在自动安装...
    %PYTHON_CMD% -m pip install Pillow
    if errorlevel 1 (
        echo ❌ 自动安装失败
        echo 💡 请手动安装：pip install Pillow
        pause
        exit /b 1
    )
    echo ✅ 图像处理库安装成功
)

REM 检查tkinter
%PYTHON_CMD% -c "import tkinter; print('✅ 图形界面库可用')" 2>nul
if errorlevel 1 (
    echo ❌ 图形界面库不可用
    echo 💡 请重新安装Python，确保包含tkinter模块
    pause
    exit /b 1
)

REM 检查程序文件
if not exist "tif_to_png_gui.py" (
    echo ❌ 找不到主程序文件
    echo 💡 请确保所有文件都在当前目录
    pause
    exit /b 1
)

if not exist "tif.py" (
    echo ❌ 找不到转换模块
    echo 💡 请确保所有文件都在当前目录
    pause
    exit /b 1
)

echo.
echo ✅ 环境检查完成
echo.

REM 创建测试图片（如果不存在）
if not exist "test_images" (
    if exist "create_test_images.py" (
        echo 🎨 首次运行，正在创建测试图片...
        %PYTHON_CMD% create_test_images.py
        echo.
    )
)

echo 🚀 启动TIF转PNG转换工具...
echo.
echo ═══════════════════════════════════════════════════════════════
echo 💡 使用提示：
echo ✅ 支持单文件转换和批量转换
echo ✅ 支持.tif和.tiff格式文件
echo ✅ 转换后的PNG文件默认保存在原目录
echo ✅ 可以使用test_images文件夹中的图片进行测试
echo ═══════════════════════════════════════════════════════════════
echo.

REM 启动GUI程序
%PYTHON_CMD% tif_to_png_gui.py

REM 程序退出处理
echo.
if errorlevel 1 (
    echo ❌ 程序运行时发生错误
    echo.
    echo 💡 可能的原因：
    echo 1. 文件权限不足
    echo 2. 磁盘空间不足
    echo 3. 图片文件损坏
    echo 4. 路径包含特殊字符
    echo.
    echo 🔧 建议解决方案：
    echo 1. 以管理员身份运行
    echo 2. 检查磁盘空间
    echo 3. 将程序移到英文路径下
    echo 4. 尝试转换其他图片
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
echo 📋 感谢使用TIF转PNG转换工具！
echo 按任意键关闭窗口...
pause >nul
