#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的TIF转PNG工具
"""

from PIL import Image
import os
import sys


def tif_to_png(input_file, output_file=None):
    """
    将TIF文件转换为PNG文件

    参数:
        input_file: 输入的TIF文件路径
        output_file: 输出的PNG文件路径（可选）
    """
    try:
        # 检查输入文件
        if not os.path.exists(input_file):
            print(f"错误: 文件不存在 - {input_file}")
            return False

        if not input_file.lower().endswith(('.tif', '.tiff')):
            print(f"错误: 不是TIF文件 - {input_file}")
            return False

        # 确定输出文件名
        if output_file is None:
            output_file = input_file.rsplit('.', 1)[0] + '.png'

        # 转换图片
        with Image.open(input_file) as img:
            # 转换颜色模式
            if img.mode in ('RGBA', 'LA'):
                # 保持透明度
                converted_img = img
            elif img.mode == 'P':
                # 调色板模式转为RGBA
                converted_img = img.convert('RGBA')
            else:
                # 其他模式转为RGB
                converted_img = img.convert('RGB')

            # 保存为PNG
            converted_img.save(output_file, 'PNG')

        print(f"✓ 转换成功: {os.path.basename(input_file)} -> {os.path.basename(output_file)}")
        return True

    except Exception as e:
        print(f"✗ 转换失败: {e}")
        return False





def batch_convert(input_dir, output_dir=None):
    """
    批量转换目录中的TIF文件

    参数:
        input_dir: 输入目录
        output_dir: 输出目录（可选）
    """
    if not os.path.isdir(input_dir):
        print(f"错误: 目录不存在 - {input_dir}")
        return

    # 查找TIF文件
    tif_files = []
    for file in os.listdir(input_dir):
        if file.lower().endswith(('.tif', '.tiff')):
            tif_files.append(os.path.join(input_dir, file))

    if not tif_files:
        print(f"在目录 {input_dir} 中未找到TIF文件")
        return

    # 创建输出目录
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"找到 {len(tif_files)} 个TIF文件，开始转换...")

    success_count = 0
    for i, tif_file in enumerate(tif_files, 1):
        print(f"[{i}/{len(tif_files)}] 处理: {os.path.basename(tif_file)}")

        if output_dir:
            output_file = os.path.join(output_dir, os.path.basename(tif_file).rsplit('.', 1)[0] + '.png')
        else:
            output_file = None

        if tif_to_png(tif_file, output_file):
            success_count += 1

    print(f"\n转换完成! 成功: {success_count}/{len(tif_files)}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  单文件转换: python tif.py input.tif [output.png]")
        print("  批量转换:   python tif.py 输入目录 [输出目录]")
        return

    input_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None

    if os.path.isfile(input_path):
        # 单文件转换
        tif_to_png(input_path, output_path)
    elif os.path.isdir(input_path):
        # 批量转换
        batch_convert(input_path, output_path)
    else:
        print(f"错误: 路径不存在 - {input_path}")


if __name__ == "__main__":
    main()