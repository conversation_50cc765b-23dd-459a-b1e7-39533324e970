#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TIF to PNG Converter
将TIF格式图片转换为PNG格式的工具

功能：
- 单个文件转换
- 批量文件转换
- 保持图像质量
- 支持多种转换选项
"""

import os
import sys
from pathlib import Path
from PIL import Image
import argparse
from typing import List, Optional
import time


class TifToPngConverter:
    """TIF到PNG转换器类"""

    def __init__(self, quality: int = 95, optimize: bool = True):
        """
        初始化转换器

        Args:
            quality: PNG压缩质量 (1-100)
            optimize: 是否优化文件大小
        """
        self.quality = quality
        self.optimize = optimize
        self.converted_count = 0
        self.failed_count = 0
        self.failed_files = []

    def convert_single_file(self, input_path: str, output_path: Optional[str] = None) -> bool:
        """
        转换单个TIF文件为PNG

        Args:
            input_path: 输入TIF文件路径
            output_path: 输出PNG文件路径（可选，默认为同目录下同名PNG文件）

        Returns:
            bool: 转换是否成功
        """
        try:
            input_path = Path(input_path)

            # 检查输入文件是否存在
            if not input_path.exists():
                print(f"错误: 文件不存在 - {input_path}")
                return False

            # 检查文件扩展名
            if input_path.suffix.lower() not in ['.tif', '.tiff']:
                print(f"错误: 不是TIF文件 - {input_path}")
                return False

            # 确定输出路径
            if output_path is None:
                output_path = input_path.with_suffix('.png')
            else:
                output_path = Path(output_path)

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 打开并转换图像
            with Image.open(input_path) as img:
                # 如果是RGBA模式，直接保存
                # 如果是其他模式，转换为RGB
                if img.mode in ('RGBA', 'LA'):
                    converted_img = img
                elif img.mode == 'P':
                    # 调色板模式转换为RGBA以保持透明度
                    converted_img = img.convert('RGBA')
                else:
                    # 其他模式转换为RGB
                    converted_img = img.convert('RGB')

                # 保存为PNG
                converted_img.save(
                    output_path,
                    'PNG',
                    optimize=self.optimize,
                    compress_level=9 if self.optimize else 6
                )

            print(f"✓ 转换成功: {input_path.name} -> {output_path.name}")
            self.converted_count += 1
            return True

        except Exception as e:
            print(f"✗ 转换失败: {input_path} - {str(e)}")
            self.failed_count += 1
            self.failed_files.append(str(input_path))
            return False

    def convert_batch(self, input_dir: str, output_dir: Optional[str] = None,
                     recursive: bool = False) -> None:
        """
        批量转换目录中的TIF文件

        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径（可选，默认为输入目录）
            recursive: 是否递归搜索子目录
        """
        input_dir = Path(input_dir)

        if not input_dir.exists() or not input_dir.is_dir():
            print(f"错误: 输入目录不存在或不是目录 - {input_dir}")
            return

        # 确定输出目录
        if output_dir is None:
            output_dir = input_dir
        else:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

        # 搜索TIF文件
        pattern = '**/*.tif*' if recursive else '*.tif*'
        tif_files = list(input_dir.glob(pattern))

        if not tif_files:
            print(f"在目录 {input_dir} 中未找到TIF文件")
            return

        print(f"找到 {len(tif_files)} 个TIF文件，开始转换...")
        start_time = time.time()

        for i, tif_file in enumerate(tif_files, 1):
            print(f"[{i}/{len(tif_files)}] 处理: {tif_file.name}")

            # 计算相对路径以保持目录结构
            if recursive:
                rel_path = tif_file.relative_to(input_dir)
                output_file = output_dir / rel_path.with_suffix('.png')
            else:
                output_file = output_dir / tif_file.with_suffix('.png').name

            self.convert_single_file(tif_file, output_file)

        # 显示统计信息
        elapsed_time = time.time() - start_time
        print(f"\n转换完成!")
        print(f"总文件数: {len(tif_files)}")
        print(f"成功转换: {self.converted_count}")
        print(f"转换失败: {self.failed_count}")
        print(f"耗时: {elapsed_time:.2f} 秒")

        if self.failed_files:
            print(f"\n失败的文件:")
            for failed_file in self.failed_files:
                print(f"  - {failed_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TIF to PNG Converter - TIF格式转PNG格式工具')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', help='输出文件或目录路径')
    parser.add_argument('-r', '--recursive', action='store_true',
                       help='递归处理子目录（仅在批量转换时有效）')
    parser.add_argument('-q', '--quality', type=int, default=95,
                       help='PNG压缩质量 (1-100, 默认: 95)')
    parser.add_argument('--no-optimize', action='store_true',
                       help='禁用文件大小优化')

    args = parser.parse_args()

    # 创建转换器
    converter = TifToPngConverter(
        quality=args.quality,
        optimize=not args.no_optimize
    )

    input_path = Path(args.input)

    if input_path.is_file():
        # 单文件转换
        print(f"转换单个文件: {input_path}")
        converter.convert_single_file(args.input, args.output)
    elif input_path.is_dir():
        # 批量转换
        print(f"批量转换目录: {input_path}")
        converter.convert_batch(args.input, args.output, args.recursive)
    else:
        print(f"错误: 输入路径不存在 - {input_path}")
        sys.exit(1)


if __name__ == "__main__":
    main()