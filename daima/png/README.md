# TIF to PNG Converter

一个功能完整的TIF格式转PNG格式的转换工具，支持命令行和图形界面两种使用方式。

## 功能特点

- ✅ 单个文件转换
- ✅ 批量文件转换
- ✅ 递归目录处理
- ✅ 保持图像质量
- ✅ 支持透明度
- ✅ 文件大小优化
- ✅ 错误处理和日志
- ✅ 进度显示
- ✅ 图形界面
- ✅ 命令行界面

## 文件说明

- `tif.py` - 核心转换器类和命令行工具
- `tif_to_png_gui.py` - 图形界面版本
- `example_usage.py` - 使用示例代码
- `README.md` - 说明文档

## 安装依赖

确保已安装必要的Python库：

```bash
pip install Pillow
```

## 使用方法

### 1. 命令行使用

#### 基本语法
```bash
python tif.py <输入路径> [选项]
```

#### 单文件转换
```bash
# 转换单个文件（输出到同目录）
python tif.py image.tif

# 指定输出文件
python tif.py image.tif -o converted.png
```

#### 批量转换
```bash
# 转换目录中的所有TIF文件
python tif.py ./images -o ./converted

# 递归转换（包括子目录）
python tif.py ./images -o ./converted -r

# 自定义质量设置
python tif.py ./images -o ./converted -q 85
```

#### 命令行选项
- `-o, --output` - 指定输出文件或目录
- `-r, --recursive` - 递归处理子目录
- `-q, --quality` - PNG压缩质量 (1-100, 默认95)
- `--no-optimize` - 禁用文件大小优化
- `-h, --help` - 显示帮助信息

### 2. 图形界面使用

启动图形界面：
```bash
python tif_to_png_gui.py
```

界面功能：
1. **转换模式选择** - 单文件或批量转换
2. **文件选择** - 选择输入文件/目录和输出位置
3. **转换选项** - 设置压缩质量、优化选项等
4. **进度显示** - 实时显示转换进度和日志
5. **一键转换** - 点击按钮开始转换

### 3. 编程使用

```python
from tif import TifToPngConverter

# 创建转换器
converter = TifToPngConverter(quality=95, optimize=True)

# 单文件转换
converter.convert_single_file('input.tif', 'output.png')

# 批量转换
converter.convert_batch('./input_dir', './output_dir', recursive=True)

# 查看转换统计
print(f"成功转换: {converter.converted_count}")
print(f"转换失败: {converter.failed_count}")
```

## 转换选项说明

### 质量设置
- **1-100**: PNG压缩质量，数值越高质量越好，文件越大
- **推荐值**: 85-95，在质量和文件大小间取得平衡

### 优化选项
- **optimize=True**: 启用文件大小优化，可能稍微增加处理时间但减小文件大小
- **optimize=False**: 禁用优化，处理速度更快

### 颜色模式处理
- **RGBA/LA模式**: 保持透明度信息
- **P模式**: 调色板模式转换为RGBA以保持透明度
- **其他模式**: 转换为RGB模式

## 示例场景

### 场景1：设计师批量转换
```bash
# 将设计稿目录中的所有TIF文件转换为PNG
python tif.py ./design_files -o ./png_files -r -q 95
```

### 场景2：扫描文档处理
```bash
# 转换扫描的TIF文档，使用较低质量以减小文件大小
python tif.py ./scanned_docs -o ./converted_docs -q 75
```

### 场景3：单个重要文件
```bash
# 高质量转换单个重要文件
python tif.py important.tif -o important_hq.png -q 100
```

## 错误处理

工具会自动处理以下情况：
- 文件不存在
- 不支持的文件格式
- 权限问题
- 磁盘空间不足
- 损坏的图像文件

所有错误都会在日志中显示，不会中断批量处理过程。

## 性能提示

1. **批量处理**: 对于大量文件，使用批量模式比逐个转换更高效
2. **质量设置**: 根据用途选择合适的质量设置
3. **优化选项**: 对于网络传输的图片建议启用优化
4. **递归处理**: 仅在需要时使用递归选项

## 技术细节

- **支持格式**: TIF, TIFF
- **输出格式**: PNG
- **颜色深度**: 支持各种颜色深度
- **透明度**: 完全支持透明度信息
- **元数据**: 基本元数据会被保留

## 故障排除

### 常见问题

1. **"PIL not found"错误**
   ```bash
   pip install Pillow
   ```

2. **权限错误**
   - 确保对输入文件有读取权限
   - 确保对输出目录有写入权限

3. **内存不足**
   - 对于超大图片，可能需要更多内存
   - 考虑分批处理

4. **转换失败**
   - 检查输入文件是否损坏
   - 确认文件格式是否正确

## 更新日志

- v1.0.0 - 初始版本，支持基本转换功能
- 包含命令行和GUI两种界面
- 支持批量处理和递归目录扫描
- 完整的错误处理和日志记录
