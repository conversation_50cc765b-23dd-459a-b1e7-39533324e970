@echo off
chcp 65001 >nul
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    TIF转PNG 拖拽转换工具                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📖 使用方法：
echo    1. 将TIF文件或文件夹拖拽到这个窗口
echo    2. 按回车键开始转换
echo    3. 转换完成的PNG文件会保存在同一目录
echo.
echo ⚡ 快捷操作：
echo    - 直接拖拽单个TIF文件进行转换
echo    - 拖拽文件夹进行批量转换
echo.
echo ═══════════════════════════════════════════════════════════════
echo.

if "%~1"=="" (
    echo 💡 请将TIF文件或文件夹拖拽到此窗口，然后按回车...
    set /p input="请输入文件或文件夹路径: "
) else (
    set "input=%~1"
    echo 🎯 检测到拖拽文件: %~1
)

echo.
echo 🚀 开始转换...
echo ═══════════════════════════════════════════════════════════════

python "%~dp0tif.py" "%input%"

echo.
echo ═══════════════════════════════════════════════════════════════
echo ✅ 转换完成！
echo.
echo 💡 提示：转换后的PNG文件已保存在原文件同一目录
echo.
pause
